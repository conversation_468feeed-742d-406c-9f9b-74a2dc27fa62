version: '3.8'

services:
  # Backend service
  backend:
    build:
      context: ./chatbot_support_backend
      dockerfile: Dockerfile
    restart: always
    ports:
      - "8000:8000"
    environment:
      # Environment setting - uses value from .env file
      - ENV=${ENV:-local}

      # Database configuration
      - DB_HOST=${DB_HOST:-db}
      - DB_PORT=${DB_PORT:-3306}
      - DB_USER=${DB_USER:-root}
      - DB_PASSWORD=${DB_PASSWORD:-rootpassword}
      - DB_NAME=${DB_NAME:-ragchatbot}
      - DATABASE_LOGGING=${DATABASE_LOGGING:-true}

      # AWS S3 configuration
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION:-us-east-1}
      - S3_BUCKET_NAME=${S3_BUCKET_NAME}
      - S3_PREFIX=${S3_PREFIX:-chatbot_support}
      - DB_SECRET_ARN=${DB_SECRET_ARN}

      # Vector store configuration
      - VECTOR_STORE_TYPE=${VECTOR_STORE_TYPE:-chroma}
      - VECTOR_STORE_DIR=${VECTOR_STORE_DIR:-/app/central_vector_store}
      - USER_DATA_DIR=${USER_DATA_DIR:-/app/user_data}

      # JWT configuration
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-your-secret-key-change-in-production}
      - JWT_ALGORITHM=${JWT_ALGORITHM:-HS256}
      - JWT_ACCESS_TOKEN_EXPIRE_MINUTES=${JWT_ACCESS_TOKEN_EXPIRE_MINUTES:-30}

      # Google AI configuration
      - GOOGLE_SERVICE_ACCOUNT_FILE=${GOOGLE_SERVICE_ACCOUNT_FILE:-/app/google_credentials.json}
      - GOOGLE_PROJECT_ID=${GOOGLE_PROJECT_ID}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}

      # OpenAI configuration (OPTIONAL)
      - OPENAI_API_KEY=${OPENAI_API_KEY}

      # CORS configuration
      - CORS_ORIGINS=${CORS_ORIGINS:-*}

      # Logging and debugging
      - PYTHONUNBUFFERED=${PYTHONUNBUFFERED:-1}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
    volumes:
      # Google credentials file (create this file with your service account key)
      - ${GOOGLE_CREDENTIALS_FILE:-./google_credentials.json}:/app/google_credentials.json:ro
      - data-volume:/app/data
      - vector-store-volume:/app/central_vector_store
      - user-data-volume:/app/user_data
    depends_on:
      db:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/docs", "||", "exit", "1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - chatbot-network

  # Database service
  db:
    image: mysql:8.0
    restart: always
    environment:
      - MYSQL_ROOT_PASSWORD=${DB_PASSWORD:-rootpassword}
      - MYSQL_DATABASE=${DB_NAME:-ragchatbot}
    volumes:
      - mysql-data:/var/lib/mysql
      - ./init_scripts/db:/docker-entrypoint-initdb.d
    ports:
      - "${DB_EXTERNAL_PORT:-3306}:3306"
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${DB_PASSWORD:-rootpassword}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - chatbot-network

volumes:
  data-volume:
    driver: local
  user-data-volume:
    driver: local
  vector-store-volume:
    driver: local
  mysql-data:
    driver: local

networks:
  chatbot-network:
    driver: bridge
